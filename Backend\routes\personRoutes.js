const express = require('express');
const router = express.Router();
const Person = require('../models/Person');

// GET /person - Get all people with filtering, sorting, and pagination
router.get('/person', async (req, res) => {
    try {
        const {
            page = 1,
            limit = 10,
            search = '',
            gender = '',
            ageMin = '',
            ageMax = '',
            sortBy = 'createdAt',
            sortOrder = 'desc',
            active = ''
        } = req.query;

        // Build filter object
        const filter = {};

        if (search) {
            filter.$or = [
                { name: { $regex: search, $options: 'i' } },
                { email: { $regex: search, $options: 'i' } },
                { mobileNumber: { $regex: search, $options: 'i' } }
            ];
        }

        if (gender) {
            filter.gender = gender;
        }

        if (ageMin || ageMax) {
            filter.age = {};
            if (ageMin) filter.age.$gte = parseInt(ageMin);
            if (ageMax) filter.age.$lte = parseInt(ageMax);
        }

        if (active !== '') {
            filter.isActive = active === 'true';
        }

        // Calculate pagination
        const pageNum = parseInt(page);
        const limitNum = parseInt(limit);
        const skip = (pageNum - 1) * limitNum;

        // Build sort object
        const sort = {};
        sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

        // Execute query with pagination
        const people = await Person.find(filter)
            .sort(sort)
            .skip(skip)
            .limit(limitNum);

        // Get total count for pagination
        const total = await Person.countDocuments(filter);
        const totalPages = Math.ceil(total / limitNum);

        res.status(200).json({
            success: true,
            count: people.length,
            total: total,
            page: pageNum,
            totalPages: totalPages,
            hasNextPage: pageNum < totalPages,
            hasPrevPage: pageNum > 1,
            data: people
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Error fetching people',
            error: error.message
        });
    }
});

// GET /person/:id - Get a single person by ID
router.get('/person/:id', async (req, res) => {
    try {
        const person = await Person.findById(req.params.id);
        if (!person) {
            return res.status(404).json({
                success: false,
                message: 'Person not found'
            });
        }
        res.status(200).json({
            success: true,
            data: person
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Error fetching person',
            error: error.message
        });
    }
});

// POST /person - Create a new person
router.post('/person', async (req, res) => {
    try {
        const { name, age, gender, mobileNumber } = req.body;

        // Check if mobile number already exists
        const existingPerson = await Person.findOne({ mobileNumber });
        if (existingPerson) {
            return res.status(400).json({
                success: false,
                message: 'Mobile number already exists'
            });
        }

        const person = new Person({
            name,
            age,
            gender,
            mobileNumber
        });

        const savedPerson = await person.save();
        res.status(201).json({
            success: true,
            message: 'Person created successfully',
            data: savedPerson
        });
    } catch (error) {
        if (error.name === 'ValidationError') {
            const errors = Object.values(error.errors).map(err => err.message);
            return res.status(400).json({
                success: false,
                message: 'Validation error',
                errors: errors
            });
        }
        res.status(500).json({
            success: false,
            message: 'Error creating person',
            error: error.message
        });
    }
});

// PUT /person/:id - Update a person by ID
router.put('/person/:id', async (req, res) => {
    try {
        const { name, age, gender, mobileNumber } = req.body;

        // Check if mobile number already exists for another person
        const existingPerson = await Person.findOne({
            mobileNumber,
            _id: { $ne: req.params.id }
        });
        if (existingPerson) {
            return res.status(400).json({
                success: false,
                message: 'Mobile number already exists'
            });
        }

        const updatedPerson = await Person.findByIdAndUpdate(
            req.params.id,
            { name, age, gender, mobileNumber },
            { new: true, runValidators: true }
        );

        if (!updatedPerson) {
            return res.status(404).json({
                success: false,
                message: 'Person not found'
            });
        }

        res.status(200).json({
            success: true,
            message: 'Person updated successfully',
            data: updatedPerson
        });
    } catch (error) {
        if (error.name === 'ValidationError') {
            const errors = Object.values(error.errors).map(err => err.message);
            return res.status(400).json({
                success: false,
                message: 'Validation error',
                errors: errors
            });
        }
        res.status(500).json({
            success: false,
            message: 'Error updating person',
            error: error.message
        });
    }
});

// DELETE /person/:id - Delete a person by ID
router.delete('/person/:id', async (req, res) => {
    try {
        const deletedPerson = await Person.findByIdAndDelete(req.params.id);

        if (!deletedPerson) {
            return res.status(404).json({
                success: false,
                message: 'Person not found'
            });
        }

        res.status(200).json({
            success: true,
            message: 'Person deleted successfully',
            data: deletedPerson
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Error deleting person',
            error: error.message
        });
    }
});

// GET /person/stats - Get statistics
router.get('/person/stats', async (req, res) => {
    try {
        const totalPeople = await Person.countDocuments();
        const activePeople = await Person.countDocuments({ isActive: true });
        const inactivePeople = totalPeople - activePeople;

        const genderStats = await Person.aggregate([
            { $group: { _id: '$gender', count: { $sum: 1 } } }
        ]);

        const ageGroupStats = await Person.aggregate([
            {
                $addFields: {
                    ageGroup: {
                        $switch: {
                            branches: [
                                { case: { $lt: ['$age', 18] }, then: 'Minor' },
                                { case: { $lt: ['$age', 30] }, then: 'Young Adult' },
                                { case: { $lt: ['$age', 50] }, then: 'Adult' },
                                { case: { $lt: ['$age', 65] }, then: 'Middle Aged' }
                            ],
                            default: 'Senior'
                        }
                    }
                }
            },
            { $group: { _id: '$ageGroup', count: { $sum: 1 } } }
        ]);

        const avgAge = await Person.aggregate([
            { $group: { _id: null, avgAge: { $avg: '$age' } } }
        ]);

        res.status(200).json({
            success: true,
            data: {
                total: totalPeople,
                active: activePeople,
                inactive: inactivePeople,
                averageAge: avgAge[0]?.avgAge || 0,
                genderDistribution: genderStats,
                ageGroupDistribution: ageGroupStats
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Error fetching statistics',
            error: error.message
        });
    }
});

// POST /person/bulk - Bulk create people
router.post('/person/bulk', async (req, res) => {
    try {
        const { people } = req.body;

        if (!Array.isArray(people) || people.length === 0) {
            return res.status(400).json({
                success: false,
                message: 'Please provide an array of people'
            });
        }

        const results = [];
        const errors = [];

        for (let i = 0; i < people.length; i++) {
            try {
                const person = new Person(people[i]);
                const savedPerson = await person.save();
                results.push(savedPerson);
            } catch (error) {
                errors.push({
                    index: i,
                    data: people[i],
                    error: error.message
                });
            }
        }

        res.status(201).json({
            success: true,
            message: `Successfully created ${results.length} people`,
            data: results,
            errors: errors.length > 0 ? errors : undefined
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Error in bulk creation',
            error: error.message
        });
    }
});

// PATCH /person/:id/toggle-status - Toggle active status
router.patch('/person/:id/toggle-status', async (req, res) => {
    try {
        const person = await Person.findById(req.params.id);

        if (!person) {
            return res.status(404).json({
                success: false,
                message: 'Person not found'
            });
        }

        person.isActive = !person.isActive;
        const updatedPerson = await person.save();

        res.status(200).json({
            success: true,
            message: `Person ${updatedPerson.isActive ? 'activated' : 'deactivated'} successfully`,
            data: updatedPerson
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Error toggling person status',
            error: error.message
        });
    }
});

module.exports = router;
