import { Component } from '@angular/core';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css']
})
export class AppComponent {
  title = 'People Management System';
  clicked = false;

  getCurrentTime(): string {
    return new Date().toLocaleString();
  }

  testClick(): void {
    this.clicked = true;
    alert('🎉 Angular is working perfectly!\n\nButton clicks and events are functional.');
  }
}
