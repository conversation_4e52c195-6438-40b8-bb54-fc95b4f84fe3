{"name": "cookie", "description": "HTTP server cookie parsing and serialization", "version": "0.5.0", "author": "<PERSON>ylman <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>>"], "license": "MIT", "keywords": ["cookie", "cookies"], "repository": "jshttp/cookie", "devDependencies": {"beautify-benchmark": "0.2.4", "benchmark": "2.1.4", "eslint": "7.32.0", "eslint-plugin-markdown": "2.2.1", "mocha": "9.2.2", "nyc": "15.1.0", "safe-buffer": "5.2.1", "top-sites": "1.1.97"}, "files": ["HISTORY.md", "LICENSE", "README.md", "SECURITY.md", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"bench": "node benchmark/index.js", "lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test", "update-bench": "node scripts/update-benchmark.js", "version": "node scripts/version-history.js && git add HISTORY.md"}}