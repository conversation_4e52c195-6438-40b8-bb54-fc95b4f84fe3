{"name": "backend", "version": "1.0.0", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["people", "management", "api", "nodejs", "mongodb"], "author": "", "license": "ISC", "description": "People Management API using Node.js and MongoDB", "dependencies": {"body-parser": "^2.2.0", "cors": "^2.8.5", "express": "^4.18.2", "mongoose": "^8.15.1", "nodemon": "^3.1.10"}}