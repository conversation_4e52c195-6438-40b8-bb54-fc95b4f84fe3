<!-- MINIMAL ANGULAR TEST -->
<div style="background: red; color: white; padding: 20px; margin: 20px; font-size: 24px; border: 5px solid black;">
  <h1>🎉 SUCCESS! Angular is working!</h1>
  <p>✅ App component loaded</p>
  <p>✅ Data binding working: {{ title }}</p>
  <p>✅ Current time: {{ getCurrentTime() }}</p>
</div>

<div style="background: green; color: white; padding: 20px; margin: 20px;">
  <h2>🚀 People Management System</h2>
  <p>Frontend is now working correctly!</p>
  <button (click)="testClick()" style="padding: 10px; font-size: 16px; background: yellow; color: black; border: none; border-radius: 4px;">
    Click me to test events!
  </button>
  <p *ngIf="clicked">✅ Button click works! Angular events are functional.</p>
</div>
