.dashboard-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #e9ecef;
}

.dashboard-header h2 {
  color: #333;
  margin: 0;
  font-size: 28px;
  font-weight: 700;
}

.btn-refresh {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0,123,255,0.3);
}

.btn-refresh:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,123,255,0.4);
}

.btn-refresh:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.loading {
  text-align: center;
  padding: 60px 20px;
  font-size: 18px;
  color: #666;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.error {
  background: linear-gradient(135deg, #f8d7da, #f5c6cb);
  color: #721c24;
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 20px;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.stat-card {
  background: white;
  padding: 25px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  gap: 20px;
  transition: all 0.3s ease;
  border-left: 4px solid;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.stat-card.total {
  border-left-color: #007bff;
  background: linear-gradient(135deg, #ffffff, #f8f9ff);
}

.stat-card.active {
  border-left-color: #28a745;
  background: linear-gradient(135deg, #ffffff, #f8fff9);
}

.stat-card.inactive {
  border-left-color: #dc3545;
  background: linear-gradient(135deg, #ffffff, #fff8f8);
}

.stat-card.average {
  border-left-color: #ffc107;
  background: linear-gradient(135deg, #ffffff, #fffef8);
}

.stat-icon {
  font-size: 32px;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  background: rgba(0,0,0,0.05);
}

.stat-content h3 {
  margin: 0;
  font-size: 32px;
  font-weight: 700;
  color: #333;
}

.stat-content p {
  margin: 5px 0 0 0;
  color: #666;
  font-size: 14px;
  font-weight: 500;
}

.charts-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 30px;
  margin-bottom: 40px;
}

.chart-card {
  background: white;
  padding: 30px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.chart-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.chart-card h3 {
  margin: 0 0 25px 0;
  color: #333;
  font-size: 20px;
  font-weight: 600;
  padding-bottom: 15px;
  border-bottom: 2px solid #f1f3f4;
}

.gender-stats, .age-stats {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.gender-item, .age-item {
  display: flex;
  align-items: center;
  gap: 15px;
}

.gender-bar, .age-bar {
  flex: 1;
  height: 12px;
  background: #f1f3f4;
  border-radius: 6px;
  overflow: hidden;
  position: relative;
}

.gender-fill, .age-fill {
  height: 100%;
  border-radius: 6px;
  transition: width 0.8s ease;
  background: linear-gradient(90deg, #007bff, #0056b3);
}

.gender-item.male .gender-fill {
  background: linear-gradient(90deg, #007bff, #0056b3);
}

.gender-item.female .gender-fill {
  background: linear-gradient(90deg, #e91e63, #c2185b);
}

.gender-item.other .gender-fill {
  background: linear-gradient(90deg, #9c27b0, #7b1fa2);
}

.gender-info, .age-info {
  min-width: 120px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.gender-label, .age-label {
  font-weight: 600;
  color: #333;
}

.gender-count, .age-count {
  font-weight: 500;
  color: #666;
  font-size: 14px;
}

.quick-actions {
  background: white;
  padding: 30px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.quick-actions h3 {
  margin: 0 0 25px 0;
  color: #333;
  font-size: 20px;
  font-weight: 600;
  padding-bottom: 15px;
  border-bottom: 2px solid #f1f3f4;
}

.action-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 20px;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  text-decoration: none;
  color: #333;
  background: white;
  transition: all 0.3s ease;
  cursor: pointer;
  font-weight: 500;
}

.action-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(0,0,0,0.1);
  text-decoration: none;
  color: #333;
}

.action-btn.add {
  border-color: #28a745;
  color: #28a745;
}

.action-btn.add:hover {
  background: #28a745;
  color: white;
}

.action-btn.view {
  border-color: #007bff;
  color: #007bff;
}

.action-btn.view:hover {
  background: #007bff;
  color: white;
}

.action-btn.export {
  border-color: #ffc107;
  color: #856404;
}

.action-btn.export:hover {
  background: #ffc107;
  color: #856404;
}

.action-icon {
  font-size: 20px;
}

.action-text {
  font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-container {
    padding: 15px;
  }

  .dashboard-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .charts-section {
    grid-template-columns: 1fr;
  }

  .action-buttons {
    grid-template-columns: 1fr;
  }

  .gender-item, .age-item {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }

  .gender-info, .age-info {
    min-width: auto;
  }
}