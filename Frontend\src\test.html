<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Static Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }
        .test-box {
            background: red;
            color: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            font-size: 18px;
        }
        .success-box {
            background: green;
            color: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="test-box">
        <h1>🧪 STATIC TEST PAGE</h1>
        <p>If you can see this, the web server is working correctly.</p>
        <p>This is a static HTML file served directly.</p>
    </div>

    <div class="success-box">
        <h2>✅ Web Server Status: WORKING</h2>
        <p>Current time: <span id="time"></span></p>
        <button onclick="testClick()">Click me to test JavaScript</button>
        <p id="result"></p>
    </div>

    <script>
        // Update time
        function updateTime() {
            document.getElementById('time').textContent = new Date().toLocaleString();
        }
        updateTime();
        setInterval(updateTime, 1000);

        // Test JavaScript
        function testClick() {
            document.getElementById('result').innerHTML = '✅ JavaScript is working!';
            alert('✅ Static page and JavaScript are working perfectly!');
        }

        console.log('🧪 Static test page loaded successfully');
    </script>
</body>
</html>
