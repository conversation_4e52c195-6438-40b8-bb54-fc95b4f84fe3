import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { tap } from 'rxjs/operators';

export interface Address {
  street?: string;
  city?: string;
  state?: string;
  zipCode?: string;
}

export interface Person {
  _id?: string;
  name: string;
  age: number;
  gender: string;
  mobileNumber: string;
  email?: string;
  address?: Address;
  isActive?: boolean;
  createdAt?: string;
  updatedAt?: string;
  displayName?: string;
  ageGroup?: string;
  fullAddress?: string;
}

export interface ApiResponse {
  success: boolean;
  message?: string;
  data?: Person | Person[] | any;
  count?: number;
  total?: number;
  page?: number;
  totalPages?: number;
  hasNextPage?: boolean;
  hasPrevPage?: boolean;
  errors?: string[];
}

export interface SearchFilters {
  page?: number;
  limit?: number;
  search?: string;
  gender?: string;
  ageMin?: number;
  ageMax?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  active?: boolean;
}

export interface Statistics {
  total: number;
  active: number;
  inactive: number;
  averageAge: number;
  genderDistribution: Array<{_id: string, count: number}>;
  ageGroupDistribution: Array<{_id: string, count: number}>;
}

@Injectable({
  providedIn: 'root'
})
export class PersonService {
  private apiUrl = 'http://localhost:3000/api/person';
  private refreshSubject = new BehaviorSubject<boolean>(false);
  public refresh$ = this.refreshSubject.asObservable();

  private httpOptions = {
    headers: new HttpHeaders({
      'Content-Type': 'application/json'
    })
  };

  constructor(private http: HttpClient) { }

  // Trigger refresh
  triggerRefresh() {
    this.refreshSubject.next(true);
  }

  // Get all people with filters
  getAllPeople(filters?: SearchFilters): Observable<ApiResponse> {
    let params = new HttpParams();

    if (filters) {
      Object.keys(filters).forEach(key => {
        const value = filters[key];
        if (value !== undefined && value !== null && value !== '') {
          params = params.set(key, value.toString());
        }
      });
    }

    return this.http.get<ApiResponse>(this.apiUrl, { params });
  }

  // Get person by ID
  getPersonById(id: string): Observable<ApiResponse> {
    return this.http.get<ApiResponse>(`${this.apiUrl}/${id}`);
  }

  // Create new person
  createPerson(person: Person): Observable<ApiResponse> {
    return this.http.post<ApiResponse>(this.apiUrl, person, this.httpOptions)
      .pipe(tap(() => this.triggerRefresh()));
  }

  // Update person
  updatePerson(id: string, person: Person): Observable<ApiResponse> {
    return this.http.put<ApiResponse>(`${this.apiUrl}/${id}`, person, this.httpOptions)
      .pipe(tap(() => this.triggerRefresh()));
  }

  // Delete person
  deletePerson(id: string): Observable<ApiResponse> {
    return this.http.delete<ApiResponse>(`${this.apiUrl}/${id}`)
      .pipe(tap(() => this.triggerRefresh()));
  }

  // Toggle person status
  togglePersonStatus(id: string): Observable<ApiResponse> {
    return this.http.patch<ApiResponse>(`${this.apiUrl}/${id}/toggle-status`, {}, this.httpOptions)
      .pipe(tap(() => this.triggerRefresh()));
  }

  // Get statistics
  getStatistics(): Observable<ApiResponse> {
    return this.http.get<ApiResponse>(`${this.apiUrl}/stats`);
  }

  // Bulk create people
  bulkCreatePeople(people: Person[]): Observable<ApiResponse> {
    return this.http.post<ApiResponse>(`${this.apiUrl}/bulk`, { people }, this.httpOptions)
      .pipe(tap(() => this.triggerRefresh()));
  }

  // Search people
  searchPeople(searchTerm: string): Observable<ApiResponse> {
    const params = new HttpParams().set('search', searchTerm);
    return this.http.get<ApiResponse>(this.apiUrl, { params });
  }

  // Export people data
  exportPeople(format: 'json' | 'csv' = 'json'): Observable<ApiResponse> {
    return this.getAllPeople({ limit: 1000 });
  }
}
