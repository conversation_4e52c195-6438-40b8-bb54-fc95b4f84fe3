const mongoose = require('mongoose');

const personSchema = new mongoose.Schema({
    name: {
        type: String,
        required: [true, 'Name is required'],
        trim: true,
        minlength: [2, 'Name must be at least 2 characters long'],
        maxlength: [50, 'Name cannot exceed 50 characters'],
        validate: {
            validator: function(v) {
                return /^[a-zA-Z\s]+$/.test(v);
            },
            message: 'Name can only contain letters and spaces'
        }
    },
    age: {
        type: Number,
        required: [true, 'Age is required'],
        min: [1, 'Age must be at least 1'],
        max: [120, 'Age cannot exceed 120'],
        validate: {
            validator: function(v) {
                return Number.isInteger(v);
            },
            message: 'Age must be a whole number'
        }
    },
    gender: {
        type: String,
        required: [true, 'Gender is required'],
        enum: {
            values: ['Male', 'Female', 'Other'],
            message: 'Gender must be Male, Female, or Other'
        },
        trim: true
    },
    mobileNumber: {
        type: String,
        required: [true, 'Mobile number is required'],
        trim: true,
        validate: {
            validator: function(v) {
                return /^\d{10}$/.test(v);
            },
            message: 'Mobile number must be exactly 10 digits'
        }
    },
    email: {
        type: String,
        trim: true,
        lowercase: true,
        validate: {
            validator: function(v) {
                return !v || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(v);
            },
            message: 'Please enter a valid email address'
        }
    },
    address: {
        street: {
            type: String,
            trim: true,
            maxlength: [100, 'Street address cannot exceed 100 characters']
        },
        city: {
            type: String,
            trim: true,
            maxlength: [50, 'City name cannot exceed 50 characters']
        },
        state: {
            type: String,
            trim: true,
            maxlength: [50, 'State name cannot exceed 50 characters']
        },
        zipCode: {
            type: String,
            trim: true,
            validate: {
                validator: function(v) {
                    return !v || /^\d{5,6}$/.test(v);
                },
                message: 'Zip code must be 5 or 6 digits'
            }
        }
    },
    isActive: {
        type: Boolean,
        default: true
    }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});

// Virtual for full name formatting
personSchema.virtual('displayName').get(function() {
    return this.name.split(' ').map(word =>
        word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
    ).join(' ');
});

// Virtual for age group
personSchema.virtual('ageGroup').get(function() {
    if (this.age < 18) return 'Minor';
    if (this.age < 30) return 'Young Adult';
    if (this.age < 50) return 'Adult';
    if (this.age < 65) return 'Middle Aged';
    return 'Senior';
});

// Virtual for full address
personSchema.virtual('fullAddress').get(function() {
    if (!this.address) return '';
    const parts = [
        this.address.street,
        this.address.city,
        this.address.state,
        this.address.zipCode
    ].filter(part => part && part.trim());
    return parts.join(', ');
});

// Add indexes for better query performance
personSchema.index({ name: 1 });
personSchema.index({ mobileNumber: 1 }, { unique: true });
personSchema.index({ email: 1 });
personSchema.index({ isActive: 1 });
personSchema.index({ createdAt: -1 });

// Pre-save middleware to format name
personSchema.pre('save', function(next) {
    if (this.name) {
        this.name = this.name.split(' ').map(word =>
            word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
        ).join(' ');
    }
    next();
});

// Static method to find active people
personSchema.statics.findActive = function() {
    return this.find({ isActive: true });
};

// Instance method to get person summary
personSchema.methods.getSummary = function() {
    return {
        id: this._id,
        name: this.displayName,
        age: this.age,
        ageGroup: this.ageGroup,
        gender: this.gender,
        mobile: this.mobileNumber,
        email: this.email || 'Not provided',
        address: this.fullAddress || 'Not provided',
        isActive: this.isActive,
        memberSince: this.createdAt.toDateString()
    };
};

const Person = mongoose.model('Person', personSchema);

module.exports = Person;
