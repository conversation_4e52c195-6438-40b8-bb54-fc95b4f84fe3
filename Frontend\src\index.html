<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>People Management System</title>
  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="icon" type="image/x-icon" href="favicon.ico">
  <style>
    body {
      margin: 0;
      padding: 20px;
      font-family: Arial, sans-serif;
      background-color: #f0f0f0;
    }
    .loading-message {
      background: orange;
      color: white;
      padding: 20px;
      margin: 20px 0;
      border-radius: 8px;
      font-size: 18px;
      text-align: center;
    }
  </style>
</head>
<body>
  <!-- Fallback content that should always be visible -->
  <div class="loading-message">
    🔄 Loading Angular Application... If this message persists, there may be a JavaScript error.
  </div>

  <!-- Angular app root -->
  <app-root>
    <!-- This content will be replaced by Angular -->
    <div style="background: yellow; padding: 20px; margin: 20px; border: 3px solid red;">
      <h2>⚠️ Angular Not Loaded</h2>
      <p>If you see this message, Angular is not loading properly.</p>
      <p>Check the browser console for JavaScript errors.</p>
    </div>
  </app-root>

  <!-- Debug script -->
  <script>
    console.log('🔧 Index.html loaded successfully');
    console.log('🔧 Current URL:', window.location.href);
    console.log('🔧 User Agent:', navigator.userAgent);

    // Check if Angular loads within 5 seconds
    setTimeout(function() {
      var appRoot = document.querySelector('app-root');
      if (appRoot && appRoot.innerHTML.includes('Angular Not Loaded')) {
        console.error('❌ Angular failed to load within 5 seconds');
        alert('❌ Angular Application Failed to Load\n\nPlease check the browser console for errors.');
      } else {
        console.log('✅ Angular appears to have loaded successfully');
      }
    }, 5000);
  </script>
</body>
</html>
