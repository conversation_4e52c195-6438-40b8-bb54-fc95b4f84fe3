<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>People Management System</title>
  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="icon" type="image/x-icon" href="favicon.ico">
  <style>
    body {
      margin: 0;
      padding: 20px;
      font-family: Arial, sans-serif;
      background-color: #f0f0f0;
    }
    .loading-message {
      background: orange;
      color: white;
      padding: 20px;
      margin: 20px 0;
      border-radius: 8px;
      font-size: 18px;
      text-align: center;
    }
  </style>
</head>
<body>
  <!-- Fallback content that should always be visible -->
  <div class="loading-message">
    🔄 Loading Angular Application... If this message persists, there may be a JavaScript error.
  </div>

  <!-- Angular app root -->
  <app-root>
    <!-- This content will be replaced by Angular -->
    <div style="background: yellow; padding: 20px; margin: 20px; border: 3px solid red;">
      <h2>⚠️ Angular Not Loaded</h2>
      <p>If you see this message, Angular is not loading properly.</p>
      <p>Check the browser console for JavaScript errors.</p>
    </div>
  </app-root>

  <!-- Enhanced Debug script -->
  <script>
    console.log('🔧 Index.html loaded successfully');
    console.log('🔧 Current URL:', window.location.href);
    console.log('🔧 User Agent:', navigator.userAgent);

    // Capture all JavaScript errors
    var errors = [];
    window.addEventListener('error', function(e) {
      var errorMsg = 'JavaScript Error: ' + e.message + ' at ' + e.filename + ':' + e.lineno;
      console.error('❌ ' + errorMsg);
      errors.push(errorMsg);

      // Display error on page
      var errorDiv = document.getElementById('js-errors') || document.createElement('div');
      errorDiv.id = 'js-errors';
      errorDiv.style.cssText = 'background: red; color: white; padding: 15px; margin: 10px; border-radius: 4px; font-family: monospace; white-space: pre-wrap;';
      errorDiv.innerHTML = '❌ JavaScript Errors Detected:\n\n' + errors.join('\n\n');
      document.body.appendChild(errorDiv);
    });

    // Check if Angular loads within 3 seconds
    setTimeout(function() {
      var appRoot = document.querySelector('app-root');
      var statusDiv = document.createElement('div');
      statusDiv.style.cssText = 'background: blue; color: white; padding: 15px; margin: 10px; border-radius: 4px; font-size: 16px;';

      if (appRoot && appRoot.innerHTML.includes('Angular Not Loaded')) {
        console.error('❌ Angular failed to load within 3 seconds');
        statusDiv.innerHTML = '❌ Angular Failed to Load<br><br>Errors found: ' + errors.length + '<br><br>Check console for details.';
        if (errors.length === 0) {
          statusDiv.innerHTML += '<br><br>No JavaScript errors detected. This might be a different issue.';
        }
      } else {
        console.log('✅ Angular appears to have loaded successfully');
        statusDiv.innerHTML = '✅ Angular Loaded Successfully!';
        statusDiv.style.background = 'green';
      }
      document.body.appendChild(statusDiv);
    }, 3000);
  </script>
</body>
</html>
