.app {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.navbar {
  background-color: #007bff;
  color: white;
  padding: 0;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

.nav-brand {
  text-decoration: none;
  color: white;
}

.nav-brand:hover {
  color: white;
  text-decoration: none;
}

.nav-brand h1 {
  margin: 0;
  padding: 15px 0;
  font-size: 24px;
  font-weight: 600;
}

.nav-menu {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  gap: 0;
}

.nav-item {
  margin: 0;
}

.nav-link {
  display: block;
  padding: 20px 25px;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: all 0.3s;
  border-bottom: 3px solid transparent;
}

.nav-link:hover {
  color: white;
  background-color: rgba(255, 255, 255, 0.1);
  text-decoration: none;
}

.nav-link.active {
  color: white;
  background-color: rgba(255, 255, 255, 0.1);
  border-bottom-color: white;
}

.main-content {
  min-height: calc(100vh - 80px);
  padding: 0;
}

/* Global styles */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Responsive */
@media (max-width: 768px) {
  .nav-container {
    flex-direction: column;
    padding: 10px 20px;
  }

  .nav-brand h1 {
    font-size: 20px;
    padding: 10px 0;
  }

  .nav-menu {
    width: 100%;
    justify-content: center;
  }

  .nav-link {
    padding: 15px 20px;
  }
}