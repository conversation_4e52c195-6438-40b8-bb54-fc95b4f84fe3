import { Component, OnInit, OnDestroy } from '@angular/core';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { PersonService, Person, ApiResponse, SearchFilters } from '../../services/person.service';

@Component({
  selector: 'app-person-list',
  templateUrl: './person-list.component.html',
  styleUrls: ['./person-list.component.css']
})
export class PersonListComponent implements OnInit, OnDestroy {
  people: Person[] = [];
  loading = false;
  error = '';

  // Pagination
  currentPage = 1;
  totalPages = 1;
  totalPeople = 0;
  pageSize = 10;

  // Search and filters
  searchTerm = '';
  selectedGender = '';
  ageMin: number | null = null;
  ageMax: number | null = null;
  sortBy = 'createdAt';
  sortOrder: 'asc' | 'desc' = 'desc';
  showActiveOnly = false;

  // Subscriptions
  private refreshSubscription: Subscription;

  constructor(
    private personService: PersonService,
    private router: Router
  ) { }

  ngOnInit() {
    this.loadPeople();

    // Subscribe to refresh events
    this.refreshSubscription = this.personService.refresh$.subscribe(
      (shouldRefresh) => {
        if (shouldRefresh) {
          this.loadPeople();
        }
      }
    );
  }

  ngOnDestroy() {
    if (this.refreshSubscription) {
      this.refreshSubscription.unsubscribe();
    }
  }

  loadPeople() {
    this.loading = true;
    this.error = '';

    const filters: SearchFilters = {
      page: this.currentPage,
      limit: this.pageSize,
      search: this.searchTerm || undefined,
      gender: this.selectedGender || undefined,
      ageMin: this.ageMin || undefined,
      ageMax: this.ageMax || undefined,
      sortBy: this.sortBy,
      sortOrder: this.sortOrder,
      active: this.showActiveOnly || undefined
    };

    this.personService.getAllPeople(filters).subscribe(
      (response: ApiResponse) => {
        this.loading = false;
        if (response.success && Array.isArray(response.data)) {
          this.people = response.data;
          this.totalPeople = response.total || 0;
          this.totalPages = response.totalPages || 1;
          this.currentPage = response.page || 1;
        } else {
          this.error = response.message || 'Failed to load people';
        }
      },
      (error) => {
        this.loading = false;
        this.error = 'Failed to connect to server';
        console.error('Error loading people:', error);
      }
    );
  }

  editPerson(id: string) {
    this.router.navigate(['/person/edit', id]);
  }

  deletePerson(id: string) {
    this.router.navigate(['/person/delete', id]);
  }

  addNewPerson() {
    this.router.navigate(['/person/new']);
  }

  // Search and filter methods
  onSearch() {
    this.currentPage = 1;
    this.loadPeople();
  }

  onFilterChange() {
    this.currentPage = 1;
    this.loadPeople();
  }

  clearFilters() {
    this.searchTerm = '';
    this.selectedGender = '';
    this.ageMin = null;
    this.ageMax = null;
    this.showActiveOnly = false;
    this.currentPage = 1;
    this.loadPeople();
  }

  // Pagination methods
  goToPage(page: number) {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
      this.loadPeople();
    }
  }

  previousPage() {
    if (this.currentPage > 1) {
      this.currentPage--;
      this.loadPeople();
    }
  }

  nextPage() {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
      this.loadPeople();
    }
  }

  // Sorting methods
  sortBy(field: string) {
    if (this.sortBy === field) {
      this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortBy = field;
      this.sortOrder = 'asc';
    }
    this.loadPeople();
  }

  // Toggle person status
  togglePersonStatus(person: Person) {
    if (person._id) {
      this.personService.togglePersonStatus(person._id).subscribe(
        (response: ApiResponse) => {
          if (response.success) {
            // Update the person in the list
            person.isActive = !person.isActive;
          } else {
            this.error = response.message || 'Failed to update person status';
          }
        },
        (error) => {
          this.error = 'Failed to connect to server';
          console.error('Error toggling person status:', error);
        }
      );
    }
  }

  // Utility methods
  getPageNumbers(): number[] {
    const pages: number[] = [];
    const start = Math.max(1, this.currentPage - 2);
    const end = Math.min(this.totalPages, this.currentPage + 2);

    for (let i = start; i <= end; i++) {
      pages.push(i);
    }

    return pages;
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString();
  }

  getStatusBadgeClass(isActive: boolean): string {
    return isActive ? 'status-active' : 'status-inactive';
  }

  getStatusText(isActive: boolean): string {
    return isActive ? 'Active' : 'Inactive';
  }
}
