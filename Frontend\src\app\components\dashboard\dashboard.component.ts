import { Component, OnInit } from '@angular/core';
import { PersonService, Statistics, ApiResponse } from '../../services/person.service';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.css']
})
export class DashboardComponent implements OnInit {
  statistics: Statistics | null = null;
  loading = false;
  error = '';

  constructor(private personService: PersonService) { }

  ngOnInit() {
    this.loadStatistics();
  }

  loadStatistics() {
    this.loading = true;
    this.error = '';

    this.personService.getStatistics().subscribe(
      (response: ApiResponse) => {
        this.loading = false;
        if (response.success && response.data) {
          this.statistics = response.data as Statistics;
        } else {
          this.error = response.message || 'Failed to load statistics';
        }
      },
      (error) => {
        this.loading = false;
        this.error = 'Failed to connect to server';
        console.error('Error loading statistics:', error);
      }
    );
  }

  getGenderPercentage(gender: string): number {
    if (!this.statistics || this.statistics.total === 0) return 0;
    const genderStat = this.statistics.genderDistribution.find(g => g._id === gender);
    return genderStat ? Math.round((genderStat.count / this.statistics.total) * 100) : 0;
  }

  getAgeGroupPercentage(ageGroup: string): number {
    if (!this.statistics || this.statistics.total === 0) return 0;
    const ageGroupStat = this.statistics.ageGroupDistribution.find(ag => ag._id === ageGroup);
    return ageGroupStat ? Math.round((ageGroupStat.count / this.statistics.total) * 100) : 0;
  }

  refreshStats() {
    this.loadStatistics();
  }
}
