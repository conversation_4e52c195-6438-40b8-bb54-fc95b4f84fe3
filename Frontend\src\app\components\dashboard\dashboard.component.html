<div class="dashboard-container">
  <div class="dashboard-header">
    <h2>📊 Dashboard</h2>
    <button class="btn btn-refresh" (click)="refreshStats()" [disabled]="loading">
      <span *ngIf="!loading">🔄 Refresh</span>
      <span *ngIf="loading">⏳ Loading...</span>
    </button>
  </div>

  <div *ngIf="loading && !statistics" class="loading">
    Loading dashboard data...
  </div>

  <div *ngIf="error" class="error">
    {{ error }}
    <button class="btn btn-secondary" (click)="refreshStats()">Try Again</button>
  </div>

  <div *ngIf="statistics && !loading" class="dashboard-content">
    <!-- Summary Cards -->
    <div class="stats-grid">
      <div class="stat-card total">
        <div class="stat-icon">👥</div>
        <div class="stat-content">
          <h3>{{ statistics.total }}</h3>
          <p>Total People</p>
        </div>
      </div>

      <div class="stat-card active">
        <div class="stat-icon">✅</div>
        <div class="stat-content">
          <h3>{{ statistics.active }}</h3>
          <p>Active</p>
        </div>
      </div>

      <div class="stat-card inactive">
        <div class="stat-icon">❌</div>
        <div class="stat-content">
          <h3>{{ statistics.inactive }}</h3>
          <p>Inactive</p>
        </div>
      </div>

      <div class="stat-card average">
        <div class="stat-icon">📈</div>
        <div class="stat-content">
          <h3>{{ statistics.averageAge | number:'1.1-1' }}</h3>
          <p>Average Age</p>
        </div>
      </div>
    </div>

    <!-- Charts Section -->
    <div class="charts-section">
      <!-- Gender Distribution -->
      <div class="chart-card">
        <h3>👫 Gender Distribution</h3>
        <div class="chart-content">
          <div class="gender-stats">
            <div class="gender-item male">
              <div class="gender-bar">
                <div class="gender-fill" [style.width.%]="getGenderPercentage('Male')"></div>
              </div>
              <div class="gender-info">
                <span class="gender-label">Male</span>
                <span class="gender-count">{{ getGenderPercentage('Male') }}%</span>
              </div>
            </div>

            <div class="gender-item female">
              <div class="gender-bar">
                <div class="gender-fill" [style.width.%]="getGenderPercentage('Female')"></div>
              </div>
              <div class="gender-info">
                <span class="gender-label">Female</span>
                <span class="gender-count">{{ getGenderPercentage('Female') }}%</span>
              </div>
            </div>

            <div class="gender-item other">
              <div class="gender-bar">
                <div class="gender-fill" [style.width.%]="getGenderPercentage('Other')"></div>
              </div>
              <div class="gender-info">
                <span class="gender-label">Other</span>
                <span class="gender-count">{{ getGenderPercentage('Other') }}%</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Age Group Distribution -->
      <div class="chart-card">
        <h3>📊 Age Group Distribution</h3>
        <div class="chart-content">
          <div class="age-stats">
            <div class="age-item" *ngFor="let ageGroup of statistics.ageGroupDistribution">
              <div class="age-bar">
                <div class="age-fill" [style.width.%]="getAgeGroupPercentage(ageGroup._id)"></div>
              </div>
              <div class="age-info">
                <span class="age-label">{{ ageGroup._id }}</span>
                <span class="age-count">{{ ageGroup.count }} ({{ getAgeGroupPercentage(ageGroup._id) }}%)</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
      <h3>⚡ Quick Actions</h3>
      <div class="action-buttons">
        <a routerLink="/person/new" class="action-btn add">
          <span class="action-icon">➕</span>
          <span class="action-text">Add New Person</span>
        </a>
        <a routerLink="/people" class="action-btn view">
          <span class="action-icon">📋</span>
          <span class="action-text">View All People</span>
        </a>
        <button class="action-btn export" (click)="refreshStats()">
          <span class="action-icon">📊</span>
          <span class="action-text">Refresh Data</span>
        </button>
      </div>
    </div>
  </div>
</div>
