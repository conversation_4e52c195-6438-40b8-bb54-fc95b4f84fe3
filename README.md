# People Management System

A full-stack web application for managing people records, built with Angular 8 frontend and Node.js/MongoDB backend.

## Features

### Frontend (Angular 8)
- **List all people**: View all people in a responsive table format
- **Add new person**: Create new person records with form validation
- **Edit person**: Update existing person information
- **Delete person**: Remove person records with confirmation dialog
- **Responsive design**: Works on desktop and mobile devices
- **Form validation**: Client-side validation for all input fields

### Backend (Node.js + Express + MongoDB)
- **RESTful API**: Complete CRUD operations for person management
- **Data validation**: Server-side validation with detailed error messages
- **MongoDB integration**: Persistent data storage with Mongoose ODM
- **CORS enabled**: Cross-origin resource sharing for frontend integration
- **Error handling**: Comprehensive error handling and logging

## Person Schema

Each person record contains:
- **Name**: String (2-50 characters, required)
- **Age**: Number (1-120, required)
- **Gender**: String (Male/Female/Other, required)
- **Mobile Number**: String (exactly 10 digits, required, unique)
- **Timestamps**: Created and updated timestamps (auto-generated)

## API Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/person` | Get all people |
| GET | `/api/person/:id` | Get person by ID |
| POST | `/api/person` | Create new person |
| PUT | `/api/person/:id` | Update person by ID |
| DELETE | `/api/person/:id` | Delete person by ID |

## Technology Stack

### Frontend
- Angular 8
- TypeScript
- RxJS
- Angular Router
- Reactive Forms
- CSS3

### Backend
- Node.js
- Express.js
- MongoDB
- Mongoose ODM
- CORS
- Body Parser

## Prerequisites

- Node.js (v14 or higher)
- MongoDB (local installation or MongoDB Atlas)
- Angular CLI (`npm install -g @angular/cli@8`)

## Installation & Setup

### 1. Clone the repository
```bash
git clone <repository-url>
cd People-Management
```

### 2. Backend Setup
```bash
cd Backend
npm install
```

Make sure MongoDB is running on your system, then start the backend server:
```bash
npm run dev
```
The backend server will run on `http://localhost:3000`

### 3. Frontend Setup
```bash
cd Frontend
npm install --legacy-peer-deps
```

Start the Angular development server:
```bash
# For Node.js v17+, use legacy OpenSSL provider
$env:NODE_OPTIONS="--openssl-legacy-provider"
ng serve
```
The frontend application will run on `http://localhost:4200`

## Usage

1. **View People**: Navigate to the home page to see all people in a table
2. **Add Person**: Click "Add New Person" button to create a new record
3. **Edit Person**: Click "Edit" button next to any person to modify their information
4. **Delete Person**: Click "Delete" button to remove a person (with confirmation)

## Project Structure

```
People-Management/
├── Backend/
│   ├── models/
│   │   └── Person.js          # MongoDB schema
│   ├── routes/
│   │   └── personRoutes.js    # API routes
│   ├── server.js              # Main server file
│   └── package.json
├── Frontend/
│   ├── src/
│   │   ├── app/
│   │   │   ├── components/
│   │   │   │   ├── person-list/
│   │   │   │   ├── person-form/
│   │   │   │   └── person-delete/
│   │   │   ├── services/
│   │   │   │   └── person.service.ts
│   │   │   ├── app-routing.module.ts
│   │   │   └── app.module.ts
│   │   └── styles.css
│   └── package.json
└── README.md
```

## API Response Format

All API responses follow this format:
```json
{
  "success": boolean,
  "message": "string (optional)",
  "data": "object or array (optional)",
  "count": "number (for list responses)",
  "errors": "array of strings (for validation errors)"
}
```

## Validation Rules

- **Name**: Required, 2-50 characters
- **Age**: Required, number between 1-120
- **Gender**: Required, must be "Male", "Female", or "Other"
- **Mobile Number**: Required, exactly 10 digits, must be unique

## Development Notes

- The application uses Angular 8 which requires Node.js legacy OpenSSL provider for newer Node.js versions
- MongoDB connection string is set to `mongodb://localhost:27017/peoplemanagement`
- CORS is enabled for all origins in development
- Form validation is implemented both client-side and server-side
- The application is responsive and works on mobile devices

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the ISC License.
