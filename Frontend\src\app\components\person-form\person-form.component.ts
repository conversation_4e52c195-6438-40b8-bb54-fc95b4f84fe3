import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { PersonService, Person, ApiResponse } from '../../services/person.service';

@Component({
  selector: 'app-person-form',
  templateUrl: './person-form.component.html',
  styleUrls: ['./person-form.component.css']
})
export class PersonFormComponent implements OnInit {
  personForm: FormGroup;
  isEditMode = false;
  personId: string | null = null;
  loading = false;
  error = '';
  success = '';

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private personService: PersonService
  ) {
    this.personForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50), Validators.pattern(/^[a-zA-Z\s]+$/)]],
      age: ['', [Validators.required, Validators.min(1), Validators.max(120)]],
      gender: ['', Validators.required],
      mobileNumber: ['', [Validators.required, Validators.pattern(/^\d{10}$/)]],
      email: ['', [Validators.email]],
      address: this.fb.group({
        street: ['', [Validators.maxLength(100)]],
        city: ['', [Validators.maxLength(50)]],
        state: ['', [Validators.maxLength(50)]],
        zipCode: ['', [Validators.pattern(/^\d{5,6}$/)]]
      })
    });
  }

  ngOnInit() {
    this.personId = this.route.snapshot.paramMap.get('id');
    this.isEditMode = !!this.personId;

    if (this.isEditMode && this.personId) {
      this.loadPerson(this.personId);
    }
  }

  loadPerson(id: string) {
    this.loading = true;
    this.personService.getPersonById(id).subscribe(
      (response: ApiResponse) => {
        this.loading = false;
        if (response.success && response.data) {
          const person = response.data as Person;
          this.personForm.patchValue({
            name: person.name,
            age: person.age,
            gender: person.gender,
            mobileNumber: person.mobileNumber,
            email: person.email || '',
            address: {
              street: (person.address && person.address.street) || '',
              city: (person.address && person.address.city) || '',
              state: (person.address && person.address.state) || '',
              zipCode: (person.address && person.address.zipCode) || ''
            }
          });
        } else {
          this.error = response.message || 'Failed to load person';
        }
      },
      (error) => {
        this.loading = false;
        this.error = 'Failed to connect to server';
        console.error('Error loading person:', error);
      }
    );
  }

  onSubmit() {
    if (this.personForm.valid) {
      this.loading = true;
      this.error = '';
      this.success = '';

      const personData: Person = this.personForm.value;

      const operation = this.isEditMode && this.personId
        ? this.personService.updatePerson(this.personId, personData)
        : this.personService.createPerson(personData);

      operation.subscribe(
        (response: ApiResponse) => {
          this.loading = false;
          if (response.success) {
            this.success = response.message || 'Person saved successfully';
            setTimeout(() => {
              this.router.navigate(['/people']);
            }, 1500);
          } else {
            this.error = response.message || 'Failed to save person';
            if (response.errors) {
              this.error += ': ' + response.errors.join(', ');
            }
          }
        },
        (error) => {
          this.loading = false;
          this.error = 'Failed to connect to server';
          console.error('Error saving person:', error);
        }
      );
    } else {
      this.markFormGroupTouched();
    }
  }

  markFormGroupTouched() {
    Object.keys(this.personForm.controls).forEach(key => {
      const control = this.personForm.get(key);
      if (control) {
        control.markAsTouched();
      }
    });
  }

  cancel() {
    this.router.navigate(['/people']);
  }

  getFieldError(fieldName: string): string {
    const field = this.personForm.get(fieldName);
    if (field && field.errors && field.touched) {
      if (field.errors['required']) return `${this.getFieldDisplayName(fieldName)} is required`;
      if (field.errors['minlength']) return `${this.getFieldDisplayName(fieldName)} must be at least ${field.errors['minlength'].requiredLength} characters`;
      if (field.errors['maxlength']) return `${this.getFieldDisplayName(fieldName)} cannot exceed ${field.errors['maxlength'].requiredLength} characters`;
      if (field.errors['min']) return `${this.getFieldDisplayName(fieldName)} must be at least ${field.errors['min'].min}`;
      if (field.errors['max']) return `${this.getFieldDisplayName(fieldName)} cannot exceed ${field.errors['max'].max}`;
      if (field.errors['email']) return 'Please enter a valid email address';
      if (field.errors['pattern']) {
        if (fieldName === 'mobileNumber') return 'Mobile number must be exactly 10 digits';
        if (fieldName === 'name') return 'Name can only contain letters and spaces';
        if (fieldName.includes('zipCode')) return 'Zip code must be 5 or 6 digits';
        return `${this.getFieldDisplayName(fieldName)} format is invalid`;
      }
    }
    return '';
  }

  getFieldDisplayName(fieldName: string): string {
    const displayNames: { [key: string]: string } = {
      'name': 'Name',
      'age': 'Age',
      'gender': 'Gender',
      'mobileNumber': 'Mobile Number',
      'email': 'Email',
      'address.street': 'Street Address',
      'address.city': 'City',
      'address.state': 'State',
      'address.zipCode': 'Zip Code'
    };
    return displayNames[fieldName] || fieldName;
  }
}
