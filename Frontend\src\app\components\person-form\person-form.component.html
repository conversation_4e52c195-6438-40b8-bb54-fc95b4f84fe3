<div class="container">
  <div class="header">
    <h2>{{ isEditMode ? 'Edit Person' : 'Add New Person' }}</h2>
  </div>

  <div *ngIf="loading" class="loading">
    {{ isEditMode ? 'Loading person data...' : 'Saving person...' }}
  </div>

  <div *ngIf="error" class="error">
    {{ error }}
  </div>

  <div *ngIf="success" class="success">
    {{ success }}
  </div>

  <form [formGroup]="personForm" (ngSubmit)="onSubmit()" *ngIf="!loading">
    <div class="form-group">
      <label for="name">Name *</label>
      <input
        type="text"
        id="name"
        formControlName="name"
        class="form-control"
        [class.error]="getFieldError('name')"
        placeholder="Enter full name"
      />
      <div class="field-error" *ngIf="getFieldError('name')">
        {{ getFieldError('name') }}
      </div>
    </div>

    <div class="form-group">
      <label for="age">Age *</label>
      <input
        type="number"
        id="age"
        formControlName="age"
        class="form-control"
        [class.error]="getFieldError('age')"
        placeholder="Enter age"
        min="1"
        max="120"
      />
      <div class="field-error" *ngIf="getFieldError('age')">
        {{ getFieldError('age') }}
      </div>
    </div>

    <div class="form-group">
      <label for="gender">Gender *</label>
      <select
        id="gender"
        formControlName="gender"
        class="form-control"
        [class.error]="getFieldError('gender')"
      >
        <option value="">Select Gender</option>
        <option value="Male">Male</option>
        <option value="Female">Female</option>
        <option value="Other">Other</option>
      </select>
      <div class="field-error" *ngIf="getFieldError('gender')">
        {{ getFieldError('gender') }}
      </div>
    </div>

    <div class="form-group">
      <label for="mobileNumber">Mobile Number *</label>
      <input
        type="tel"
        id="mobileNumber"
        formControlName="mobileNumber"
        class="form-control"
        [class.error]="getFieldError('mobileNumber')"
        placeholder="Enter 10-digit mobile number"
        maxlength="10"
      />
      <div class="field-error" *ngIf="getFieldError('mobileNumber')">
        {{ getFieldError('mobileNumber') }}
      </div>
    </div>

    <div class="form-group">
      <label for="email">Email Address</label>
      <input
        type="email"
        id="email"
        formControlName="email"
        class="form-control"
        [class.error]="getFieldError('email')"
        placeholder="Enter email address (optional)"
      />
      <div class="field-error" *ngIf="getFieldError('email')">
        {{ getFieldError('email') }}
      </div>
    </div>

    <!-- Address Section -->
    <div class="address-section" formGroupName="address">
      <h3>📍 Address Information (Optional)</h3>

      <div class="form-group">
        <label for="street">Street Address</label>
        <input
          type="text"
          id="street"
          formControlName="street"
          class="form-control"
          [class.error]="getFieldError('address.street')"
          placeholder="Enter street address"
          maxlength="100"
        />
        <div class="field-error" *ngIf="getFieldError('address.street')">
          {{ getFieldError('address.street') }}
        </div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label for="city">City</label>
          <input
            type="text"
            id="city"
            formControlName="city"
            class="form-control"
            [class.error]="getFieldError('address.city')"
            placeholder="Enter city"
            maxlength="50"
          />
          <div class="field-error" *ngIf="getFieldError('address.city')">
            {{ getFieldError('address.city') }}
          </div>
        </div>

        <div class="form-group">
          <label for="state">State</label>
          <input
            type="text"
            id="state"
            formControlName="state"
            class="form-control"
            [class.error]="getFieldError('address.state')"
            placeholder="Enter state"
            maxlength="50"
          />
          <div class="field-error" *ngIf="getFieldError('address.state')">
            {{ getFieldError('address.state') }}
          </div>
        </div>

        <div class="form-group">
          <label for="zipCode">Zip Code</label>
          <input
            type="text"
            id="zipCode"
            formControlName="zipCode"
            class="form-control"
            [class.error]="getFieldError('address.zipCode')"
            placeholder="Enter zip code"
            maxlength="6"
          />
          <div class="field-error" *ngIf="getFieldError('address.zipCode')">
            {{ getFieldError('address.zipCode') }}
          </div>
        </div>
      </div>
    </div>

    <div class="form-actions">
      <button type="submit" class="btn btn-primary" [disabled]="loading">
        {{ isEditMode ? 'Update Person' : 'Create Person' }}
      </button>
      <button type="button" class="btn btn-secondary" (click)="cancel()" [disabled]="loading">
        Cancel
      </button>
    </div>
  </form>
</div>
